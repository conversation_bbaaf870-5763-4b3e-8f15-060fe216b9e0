/**
 * JSON API 版本更新使用示例
 * 
 * 本文件展示了如何使用JSON API来检查应用版本更新
 */

import { checkVersionFromAPI, createVersionCheckConfig, onUpdateStatus } from '../utils/update'
import type { VersionApiResponse } from '../../electron/types/update-service'

/**
 * 示例1: 基本的JSON API版本检查
 */
export async function basicVersionCheck() {
  console.log('=== 基本版本检查示例 ===')
  
  // 创建配置
  const config = createVersionCheckConfig('https://api.example.com/version')
  
  try {
    const result = await checkVersionFromAPI(config)
    
    if (result.success && result.data) {
      console.log('API响应成功:', result.data)
      
      if (result.data.success && result.data.data) {
        console.log('最新版本:', result.data.data.version)
        console.log('发布说明:', result.data.data.releaseNotes)
        console.log('下载链接:', result.data.data.downloadUrl)
      }
    } else {
      console.error('版本检查失败:', result.error)
    }
  } catch (error) {
    console.error('版本检查异常:', error)
  }
}

/**
 * 示例2: 带自定义配置的版本检查
 */
export async function advancedVersionCheck() {
  console.log('=== 高级版本检查示例 ===')
  
  // 创建带自定义配置的版本检查配置
  const config = createVersionCheckConfig('https://api.example.com/version', {
    timeout: 15000, // 15秒超时
    headers: {
      'Authorization': 'Bearer your-token',
      'X-App-Version': '1.0.0',
      'X-Platform': process.platform,
    }
  })
  
  try {
    const result = await checkVersionFromAPI(config)
    
    if (result.success && result.data) {
      handleVersionResponse(result.data)
    } else {
      console.error('版本检查失败:', result.error)
    }
  } catch (error) {
    console.error('版本检查异常:', error)
  }
}

/**
 * 示例3: 监听更新状态变化
 */
export function setupUpdateStatusListener() {
  console.log('=== 设置更新状态监听器 ===')
  
  const removeListener = onUpdateStatus((updateInfo) => {
    console.log('更新状态变化:', updateInfo)
    
    switch (updateInfo.status) {
      case 'checking':
        console.log('正在检查更新...')
        break
      case 'available':
        console.log(`发现新版本: ${updateInfo.version}`)
        if (updateInfo.releaseNotes) {
          console.log('更新说明:', updateInfo.releaseNotes)
        }
        break
      case 'not-available':
        console.log('当前已是最新版本')
        break
      case 'error':
        console.error('更新检查失败:', updateInfo.error)
        break
    }
  })
  
  // 返回取消监听的函数
  return removeListener
}

/**
 * 处理版本响应的辅助函数
 */
function handleVersionResponse(response: VersionApiResponse) {
  console.log('处理版本响应:', response)
  
  if (!response.success) {
    console.error('API返回错误:', response.error)
    return
  }
  
  if (!response.data) {
    console.warn('API响应中没有版本数据')
    return
  }
  
  const versionData = response.data
  
  console.log('版本信息:')
  console.log('- 版本号:', versionData.version)
  console.log('- 发布时间:', versionData.publishedAt)
  console.log('- 是否强制更新:', versionData.isForced ? '是' : '否')
  console.log('- 最低支持版本:', versionData.minVersion)
  
  if (versionData.releaseNotes) {
    console.log('- 发布说明:', versionData.releaseNotes)
  }
  
  if (versionData.downloadUrl) {
    console.log('- 下载链接:', versionData.downloadUrl)
  }
}

/**
 * 示例4: 完整的版本检查流程
 */
export async function completeVersionCheckFlow() {
  console.log('=== 完整版本检查流程示例 ===')
  
  // 1. 设置状态监听器
  const removeListener = setupUpdateStatusListener()
  
  try {
    // 2. 执行版本检查
    await advancedVersionCheck()
    
    // 3. 等待一段时间让状态更新完成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
  } finally {
    // 4. 清理监听器
    removeListener()
  }
}

/**
 * 示例JSON API响应格式
 * 
 * 您的服务器应该返回类似以下格式的JSON响应：
 * 
 * 成功响应:
 * {
 *   "success": true,
 *   "data": {
 *     "version": "1.2.0",
 *     "releaseNotes": "修复了一些bug，增加了新功能",
 *     "downloadUrl": "https://example.com/download/app-1.2.0.exe",
 *     "publishedAt": "2024-01-15T10:30:00Z",
 *     "isForced": false,
 *     "minVersion": "1.0.0"
 *   }
 * }
 * 
 * 错误响应:
 * {
 *   "success": false,
 *   "error": "版本信息获取失败"
 * }
 */

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  console.log('开始运行所有JSON API版本检查示例...\n')
  
  try {
    await basicVersionCheck()
    console.log('\n')
    
    await advancedVersionCheck()
    console.log('\n')
    
    await completeVersionCheckFlow()
    console.log('\n')
    
    console.log('所有示例运行完成!')
  } catch (error) {
    console.error('运行示例时出错:', error)
  }
}
