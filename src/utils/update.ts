import type { UpdateInfo, VersionApiResponse, VersionCheckConfig } from '../../electron/types/update-service'
import { UpdateService } from '../../electron/render/services/update-service'

/**
 * 服务结果类型定义
 */
interface ServiceResult<T = void> {
  success: boolean
  data?: T
  error?: string
}

/**
 * 更新服务工具函数
 * 使用现有的IPC系统进行更新相关操作
 */

/**
 * 检查更新
 */
export async function checkForUpdates(): Promise<ServiceResult<void>> {
  try {
    const result = await UpdateService.checkForUpdates()
    return result
  }
  catch (error) {
    console.error('检查更新失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '检查更新失败',
    }
  }
}

/**
 * 下载更新
 */
export async function downloadUpdate(): Promise<ServiceResult<void>> {
  try {
    const result = await UpdateService.downloadUpdate()
    return result
  }
  catch (error) {
    console.error('下载更新失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '下载更新失败',
    }
  }
}

/**
 * 安装更新
 */
export async function installUpdate(): Promise<ServiceResult<void>> {
  try {
    const result = await UpdateService.installUpdate()
    return result
  }
  catch (error) {
    console.error('安装更新失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '安装更新失败',
    }
  }
}

/**
 * 获取更新信息
 */
export async function getUpdateInfo(): Promise<UpdateInfo> {
  try {
    const result = await UpdateService.getUpdateInfo()
    return result
  }
  catch (error) {
    console.error('获取更新信息失败:', error)
    return { status: 'not-available' as any }
  }
}

/**
 * 获取当前版本
 */
export async function getCurrentVersion(): Promise<string> {
  try {
    const result = await UpdateService.getCurrentVersion()
    return result
  }
  catch (error) {
    console.error('获取当前版本失败:', error)
    return '未知版本'
  }
}

/**
 * 监听更新状态变化
 */
export function onUpdateStatus(listener: (info: UpdateInfo) => void): () => void {
  // 添加监听器
  UpdateService.addUpdateListener(listener)

  // 返回取消监听的函数
  return () => {
    UpdateService.removeUpdateListener(listener)
  }
}

/**
 * 初始化更新服务
 * 在应用启动时调用
 */
export function initializeUpdateService(): void {
  // 初始化UpdateService
  UpdateService.initialize()
}

/**
 * 检查是否在Electron环境中
 */
export function isElectronEnvironment(): boolean {
  return typeof window !== 'undefined' && !!window.optimizedIPC
}

/**
 * 更新状态文本映射
 */
export function getUpdateStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'checking': '正在检查更新...',
    'available': '发现新版本',
    'not-available': '当前已是最新版本',
    'downloading': '正在下载更新...',
    'downloaded': '更新下载完成',
    'installing': '正在安装更新...',
    'error': '更新失败',
  }

  return statusMap[status] || '未知状态'
}

/**
 * 格式化下载速度
 */
export function formatDownloadSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond < 1024)
    return `${bytesPerSecond.toFixed(0)} B/s`
  if (bytesPerSecond < 1024 * 1024)
    return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
  return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes < 1024)
    return `${bytes} B`
  if (bytes < 1024 * 1024)
    return `${(bytes / 1024).toFixed(1)} KB`
  if (bytes < 1024 * 1024 * 1024)
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
}

/**
 * 通过JSON API检查版本更新
 * @param config 版本检查配置
 */
export async function checkVersionFromAPI(config: VersionCheckConfig): Promise<ServiceResult<VersionApiResponse>> {
  try {
    const result = await UpdateService.checkVersionFromAPI(config)
    return result
  }
  catch (error) {
    console.error('从API检查版本失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '从API检查版本失败',
    }
  }
}

/**
 * 创建默认的版本检查配置
 * @param apiUrl API接口地址
 * @param options 可选配置
 */
export function createVersionCheckConfig(
  apiUrl: string,
  options?: Partial<Omit<VersionCheckConfig, 'apiUrl'>>
): VersionCheckConfig {
  return {
    apiUrl,
    timeout: options?.timeout || 10000,
    headers: options?.headers || {},
  }
}
