import type { UpdateInfo } from '../../electron/types/update-service'
import { UpdateService } from '../../electron/render/services/update-service'

/**
 * 服务结果类型定义
 */
interface ServiceResult<T = void> {
  success: boolean
  data?: T
  error?: string
}

/**
 * 统一更新服务工具函数
 * 整合 electron-updater 和 JSON API，避免功能重复
 */

/**
 * 统一检查更新
 * 自动选择最优的更新方式（electron-updater 或 JSON API）
 */
export async function checkForUpdates(): Promise<ServiceResult<{
  hasUpdate: boolean
  source?: 'electron-updater' | 'json-api'
  updateInfo: UpdateInfo
  recommendation?: string
}>> {
  try {
    // 使用统一的更新检查服务
    const result = await UpdateService.checkForUpdates()

    // 适配返回类型
    if (result.success) {
      return {
        success: true,
        data: {
          hasUpdate: true, // 如果检查成功，说明有更新或已是最新
          updateInfo: await getUpdateInfo(),
          recommendation: '使用统一更新服务检查',
        },
      }
    }

    return result as any
  }
  catch (error) {
    console.error('检查更新失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '检查更新失败',
    }
  }
}

/**
 * 下载更新
 */
export async function downloadUpdate(): Promise<ServiceResult<void>> {
  try {
    const result = await UpdateService.downloadUpdate()
    return result
  }
  catch (error) {
    console.error('下载更新失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '下载更新失败',
    }
  }
}

/**
 * 安装更新
 */
export async function installUpdate(): Promise<ServiceResult<void>> {
  try {
    const result = await UpdateService.installUpdate()
    return result
  }
  catch (error) {
    console.error('安装更新失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '安装更新失败',
    }
  }
}

/**
 * 获取更新信息
 */
export async function getUpdateInfo(): Promise<UpdateInfo> {
  try {
    const result = await UpdateService.getUpdateInfo()
    return result
  }
  catch (error) {
    console.error('获取更新信息失败:', error)
    return { status: 'not-available' as any }
  }
}

/**
 * 获取当前版本
 */
export async function getCurrentVersion(): Promise<string> {
  try {
    const result = await UpdateService.getCurrentVersion()
    return result
  }
  catch (error) {
    console.error('获取当前版本失败:', error)
    return '未知版本'
  }
}

/**
 * 监听更新状态变化
 */
export function onUpdateStatus(listener: (info: UpdateInfo) => void): () => void {
  // 添加监听器
  UpdateService.addUpdateListener(listener)

  // 返回取消监听的函数
  return () => {
    UpdateService.removeUpdateListener(listener)
  }
}

/**
 * 初始化更新服务
 * 在应用启动时调用
 */
export function initializeUpdateService(): void {
  // 初始化UpdateService
  UpdateService.initialize()
}

/**
 * 检查是否在Electron环境中
 */
export function isElectronEnvironment(): boolean {
  return typeof window !== 'undefined' && !!window.optimizedIPC
}

/**
 * 更新状态文本映射
 */
export function getUpdateStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'checking': '正在检查更新...',
    'available': '发现新版本',
    'not-available': '当前已是最新版本',
    'downloading': '正在下载更新...',
    'downloaded': '更新下载完成',
    'installing': '正在安装更新...',
    'error': '更新失败',
  }

  return statusMap[status] || '未知状态'
}

/**
 * 格式化下载速度
 */
export function formatDownloadSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond < 1024)
    return `${bytesPerSecond.toFixed(0)} B/s`
  if (bytesPerSecond < 1024 * 1024)
    return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
  return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes < 1024)
    return `${bytes} B`
  if (bytes < 1024 * 1024)
    return `${(bytes / 1024).toFixed(1)} KB`
  if (bytes < 1024 * 1024 * 1024)
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
}

/**
 * 配置更新服务
 * 设置更新源和策略
 */
export function configureUpdateService(_config: {
  primarySource?: 'electron-updater' | 'json-api' | 'hybrid'
  jsonApiConfig?: {
    enabled: boolean
    url: string
    timeout?: number
    headers?: Record<string, string>
  }
  electronUpdaterConfig?: {
    enabled: boolean
    url: string
    channel?: string
  }
}): void {
  // 这里可以通过IPC调用主进程的配置更新方法
  // 实际项目中应该通过IPC与主进程通信
  if (typeof window !== 'undefined' && window.optimizedIPC) {
    // window.optimizedIPC.invoke('update:configure', config)
  }
}

/**
 * 获取更新建议
 * 根据当前环境和网络状况提供更新建议
 */
export async function getUpdateRecommendation(): Promise<ServiceResult<{
  recommended: 'electron-updater' | 'json-api'
  reasons: string[]
  strategy: string
}>> {
  try {
    // 这里可以集成智能策略分析
    return {
      success: true,
      data: {
        recommended: 'hybrid' as any,
        reasons: ['自动选择最优更新方式', '支持增量和全量更新'],
        strategy: '智能混合策略',
      },
    }
  }
  catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取更新建议失败',
    }
  }
}
