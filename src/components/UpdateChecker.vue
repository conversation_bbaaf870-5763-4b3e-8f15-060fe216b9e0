<template>
  <div class="update-checker">
    <div class="update-status">
      <h3>版本更新检查</h3>
      
      <!-- 当前版本信息 -->
      <div class="current-version">
        <span>当前版本: {{ currentVersion }}</span>
      </div>
      
      <!-- 更新状态显示 -->
      <div class="status-display" :class="statusClass">
        <div class="status-icon">
          <i :class="statusIcon"></i>
        </div>
        <div class="status-text">
          {{ statusText }}
        </div>
      </div>
      
      <!-- 版本信息 -->
      <div v-if="updateInfo.version && updateInfo.status === 'available'" class="version-info">
        <h4>新版本: {{ updateInfo.version }}</h4>
        <div v-if="updateInfo.releaseNotes" class="release-notes">
          <p>更新说明:</p>
          <div class="notes-content">{{ updateInfo.releaseNotes }}</div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <button 
          @click="checkForUpdates" 
          :disabled="isChecking"
          class="btn btn-primary"
        >
          {{ isChecking ? '检查中...' : '检查更新' }}
        </button>
        
        <button 
          @click="checkFromAPI" 
          :disabled="isChecking"
          class="btn btn-secondary"
        >
          {{ isChecking ? '检查中...' : '从API检查' }}
        </button>
        
        <button 
          v-if="updateInfo.status === 'available'" 
          @click="downloadUpdate"
          :disabled="isDownloading"
          class="btn btn-success"
        >
          {{ isDownloading ? '下载中...' : '下载更新' }}
        </button>
        
        <button 
          v-if="updateInfo.status === 'downloaded'" 
          @click="installUpdate"
          class="btn btn-warning"
        >
          安装并重启
        </button>
      </div>
      
      <!-- 下载进度 -->
      <div v-if="updateInfo.progress" class="download-progress">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: updateInfo.progress.percent + '%' }"
          ></div>
        </div>
        <div class="progress-info">
          <span>{{ Math.round(updateInfo.progress.percent) }}%</span>
          <span>{{ formatDownloadSpeed(updateInfo.progress.bytesPerSecond) }}</span>
          <span>{{ formatFileSize(updateInfo.progress.transferred) }} / {{ formatFileSize(updateInfo.progress.total) }}</span>
        </div>
      </div>
    </div>
    
    <!-- API配置 -->
    <div class="api-config">
      <h4>API配置</h4>
      <div class="config-form">
        <div class="form-group">
          <label>API地址:</label>
          <input 
            v-model="apiConfig.apiUrl" 
            type="text" 
            placeholder="https://api.example.com/version"
            class="form-input"
          >
        </div>
        <div class="form-group">
          <label>超时时间(毫秒):</label>
          <input 
            v-model.number="apiConfig.timeout" 
            type="number" 
            min="1000" 
            max="60000"
            class="form-input"
          >
        </div>
        <div class="form-group">
          <label>自定义请求头:</label>
          <textarea 
            v-model="headersText" 
            placeholder='{"Authorization": "Bearer token"}'
            class="form-textarea"
            rows="3"
          ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  checkForUpdates, 
  downloadUpdate as downloadUpdateUtil, 
  installUpdate as installUpdateUtil,
  getCurrentVersion,
  onUpdateStatus,
  getUpdateStatusText,
  formatDownloadSpeed,
  formatFileSize,
  checkVersionFromAPI,
  createVersionCheckConfig
} from '../utils/update'
import type { UpdateInfo } from '../../electron/types/update-service'

// 响应式数据
const currentVersion = ref('未知')
const updateInfo = ref<UpdateInfo>({ status: 'not-available' as any })
const isChecking = ref(false)
const isDownloading = ref(false)

// API配置
const apiConfig = ref({
  apiUrl: 'https://api.example.com/version',
  timeout: 10000
})

const headersText = ref('{}')

// 计算属性
const statusText = computed(() => getUpdateStatusText(updateInfo.value.status))

const statusClass = computed(() => {
  const status = updateInfo.value.status
  return {
    'status-checking': status === 'checking',
    'status-available': status === 'available',
    'status-not-available': status === 'not-available',
    'status-downloading': status === 'downloading',
    'status-downloaded': status === 'downloaded',
    'status-error': status === 'error'
  }
})

const statusIcon = computed(() => {
  const status = updateInfo.value.status
  const iconMap: Record<string, string> = {
    'checking': 'fas fa-spinner fa-spin',
    'available': 'fas fa-download',
    'not-available': 'fas fa-check-circle',
    'downloading': 'fas fa-cloud-download-alt',
    'downloaded': 'fas fa-check',
    'error': 'fas fa-exclamation-triangle',
    'installing': 'fas fa-cog fa-spin'
  }
  return iconMap[status] || 'fas fa-question-circle'
})

// 方法
const checkForUpdates = async () => {
  if (isChecking.value) return
  
  isChecking.value = true
  try {
    await checkForUpdates()
  } catch (error) {
    console.error('检查更新失败:', error)
  } finally {
    isChecking.value = false
  }
}

const checkFromAPI = async () => {
  if (isChecking.value) return
  
  isChecking.value = true
  try {
    // 解析自定义请求头
    let headers = {}
    try {
      headers = JSON.parse(headersText.value)
    } catch (error) {
      console.warn('请求头格式错误，使用默认值')
    }
    
    // 创建配置
    const config = createVersionCheckConfig(apiConfig.value.apiUrl, {
      timeout: apiConfig.value.timeout,
      headers
    })
    
    const result = await checkVersionFromAPI(config)
    
    if (result.success) {
      console.log('API检查成功:', result.data)
    } else {
      console.error('API检查失败:', result.error)
    }
  } catch (error) {
    console.error('从API检查版本失败:', error)
  } finally {
    isChecking.value = false
  }
}

const downloadUpdate = async () => {
  if (isDownloading.value) return
  
  isDownloading.value = true
  try {
    await downloadUpdateUtil()
  } catch (error) {
    console.error('下载更新失败:', error)
  } finally {
    isDownloading.value = false
  }
}

const installUpdate = async () => {
  try {
    await installUpdateUtil()
  } catch (error) {
    console.error('安装更新失败:', error)
  }
}

// 生命周期
let removeUpdateListener: (() => void) | null = null

onMounted(async () => {
  // 获取当前版本
  try {
    currentVersion.value = await getCurrentVersion()
  } catch (error) {
    console.error('获取当前版本失败:', error)
  }
  
  // 设置更新状态监听器
  removeUpdateListener = onUpdateStatus((info) => {
    updateInfo.value = info
  })
})

onUnmounted(() => {
  // 清理监听器
  if (removeUpdateListener) {
    removeUpdateListener()
  }
})
</script>

<style scoped>
.update-checker {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.update-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.current-version {
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.status-display {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.status-display.status-checking {
  background: #e3f2fd;
  color: #1976d2;
}

.status-display.status-available {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-display.status-not-available {
  background: #e8f5e8;
  color: #388e3c;
}

.status-display.status-downloading {
  background: #fff3e0;
  color: #f57c00;
}

.status-display.status-downloaded {
  background: #e8f5e8;
  color: #388e3c;
}

.status-display.status-error {
  background: #ffebee;
  color: #d32f2f;
}

.status-icon {
  margin-right: 10px;
  font-size: 18px;
}

.version-info {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.version-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.release-notes p {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.notes-content {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1565c0;
}

.btn-secondary {
  background: #757575;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #616161;
}

.btn-success {
  background: #388e3c;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #2e7d32;
}

.btn-warning {
  background: #f57c00;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #ef6c00;
}

.download-progress {
  margin-top: 15px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: #4caf50;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.api-config {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.api-config h4 {
  margin: 0 0 15px 0;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.form-textarea {
  resize: vertical;
  font-family: monospace;
}
</style>
