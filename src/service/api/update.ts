import { request } from '../request'

interface VersionApiResponse {
  version: string // 最新版本号
  releaseNotes?: string // 发布说明
  downloadUrl?: string // 下载链接
  publishedAt?: string // 发布时间
  isForced?: boolean // 是否强制更新
  minVersion?: string // 最低支持版本
}

/**
 * 版本更新相关API接口
 */

/**
 * 通过JSON API检查版本更新
 * @param config 版本检查配置
 * @returns 返回版本信息
 */
export function fetchVersionInfo() {
  return request<VersionApiResponse>({
    url: '/api/version',
    method: 'get',
    params: {},
  })
}
