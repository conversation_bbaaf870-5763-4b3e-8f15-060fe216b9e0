import { app } from 'electron'
import fs from 'fs/promises'
import path from 'path'
import crypto from 'crypto'
import { BaseService } from './base-service'
import type { ServiceResult } from './base-service'
import type { 
  IncrementalUpdateInfo, 
  FileDiff, 
  UpdateType, 
  ExtendedVersionApiResponse 
} from '../../types/update-service'

/**
 * 增量更新服务
 * 负责处理增量更新的检查、下载、应用和回滚
 */
export class IncrementalUpdateService extends BaseService {
  protected static serviceName = 'IncrementalUpdateService'
  
  private static readonly BACKUP_DIR = path.join(app.getPath('userData'), 'update-backup')
  private static readonly TEMP_DIR = path.join(app.getPath('userData'), 'update-temp')
  private static readonly MANIFEST_FILE = 'app-manifest.json'
  
  /**
   * 应用清单接口
   */
  interface AppManifest {
    version: string
    files: Record<string, {
      hash: string
      size: number
      path: string
    }>
    timestamp: number
  }

  /**
   * 初始化增量更新服务
   */
  static async initialize(): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        // 创建必要的目录
        await this.ensureDirectories()
        
        // 生成当前应用的清单文件
        await this.generateAppManifest()
        
        this.logInfo('增量更新服务初始化完成', 'initialize')
      },
      'initialize',
      '增量更新服务初始化失败'
    )
  }

  /**
   * 检查是否支持增量更新
   */
  static async checkIncrementalUpdateSupport(
    currentVersion: string,
    targetVersion: string,
    apiResponse: ExtendedVersionApiResponse
  ): Promise<ServiceResult<boolean>> {
    return this.safeExecute(
      async () => {
        // 检查API响应是否包含增量更新信息
        if (!apiResponse.data?.incrementalUpdate) {
          this.logInfo('服务器不提供增量更新信息', 'checkIncrementalUpdateSupport')
          return false
        }

        const incrementalInfo = apiResponse.data.incrementalUpdate
        
        // 检查版本兼容性
        if (incrementalInfo.fromVersion !== currentVersion) {
          this.logInfo(`版本不匹配: 当前${currentVersion}, 需要${incrementalInfo.fromVersion}`, 'checkIncrementalUpdateSupport')
          return false
        }

        // 检查平台支持
        const supportedPlatforms = apiResponse.data.supportedPlatforms || []
        if (supportedPlatforms.length > 0 && !supportedPlatforms.includes(process.platform)) {
          this.logInfo(`当前平台${process.platform}不支持增量更新`, 'checkIncrementalUpdateSupport')
          return false
        }

        // 检查最低版本要求
        const minimumVersion = apiResponse.data.minimumVersion
        if (minimumVersion && this.compareVersions(currentVersion, minimumVersion) < 0) {
          this.logInfo(`版本过低，需要${minimumVersion}以上才支持增量更新`, 'checkIncrementalUpdateSupport')
          return false
        }

        this.logInfo('支持增量更新', 'checkIncrementalUpdateSupport')
        return true
      },
      'checkIncrementalUpdateSupport',
      '检查增量更新支持失败'
    )
  }

  /**
   * 计算增量更新差异
   */
  static async calculateUpdateDiff(
    incrementalInfo: IncrementalUpdateInfo
  ): Promise<ServiceResult<FileDiff[]>> {
    return this.safeExecute(
      async () => {
        const currentManifest = await this.loadAppManifest()
        if (!currentManifest) {
          throw new Error('无法加载当前应用清单')
        }

        const fileDiffs: FileDiff[] = []
        
        // 分析文件差异
        for (const diff of incrementalInfo.fileDiffs) {
          const currentFile = currentManifest.files[diff.path]
          
          if (diff.action === 'add') {
            // 新增文件
            fileDiffs.push(diff)
          } else if (diff.action === 'modify') {
            // 修改文件 - 检查哈希值
            if (!currentFile || currentFile.hash !== diff.hash) {
              fileDiffs.push(diff)
            }
          } else if (diff.action === 'delete') {
            // 删除文件 - 检查文件是否存在
            if (currentFile) {
              fileDiffs.push(diff)
            }
          }
        }

        this.logInfo(`计算出${fileDiffs.length}个文件需要更新`, 'calculateUpdateDiff')
        return fileDiffs
      },
      'calculateUpdateDiff',
      '计算更新差异失败'
    )
  }

  /**
   * 下载增量更新文件
   */
  static async downloadIncrementalFiles(
    fileDiffs: FileDiff[],
    onProgress?: (progress: { percent: number; current: number; total: number }) => void
  ): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        const downloadFiles = fileDiffs.filter(diff => 
          (diff.action === 'add' || diff.action === 'modify') && diff.downloadUrl
        )

        let downloadedCount = 0
        const totalCount = downloadFiles.length

        for (const file of downloadFiles) {
          if (!file.downloadUrl) continue

          // 下载文件到临时目录
          const tempFilePath = path.join(this.TEMP_DIR, file.path)
          await this.ensureDirectoryExists(path.dirname(tempFilePath))
          
          await this.downloadFile(file.downloadUrl, tempFilePath)
          
          // 验证文件哈希
          const downloadedHash = await this.calculateFileHash(tempFilePath)
          if (downloadedHash !== file.hash) {
            throw new Error(`文件${file.path}哈希验证失败`)
          }

          downloadedCount++
          
          // 报告进度
          if (onProgress) {
            onProgress({
              percent: (downloadedCount / totalCount) * 100,
              current: downloadedCount,
              total: totalCount
            })
          }
        }

        this.logInfo(`成功下载${downloadedCount}个文件`, 'downloadIncrementalFiles')
      },
      'downloadIncrementalFiles',
      '下载增量更新文件失败'
    )
  }

  /**
   * 应用增量更新
   */
  static async applyIncrementalUpdate(
    fileDiffs: FileDiff[],
    enableBackup: boolean = true
  ): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        const appPath = app.getAppPath()
        
        // 创建备份
        if (enableBackup) {
          await this.createBackup(fileDiffs)
        }

        try {
          // 应用文件更改
          for (const diff of fileDiffs) {
            const targetPath = path.join(appPath, diff.path)
            
            if (diff.action === 'add' || diff.action === 'modify') {
              // 添加或修改文件
              const tempFilePath = path.join(this.TEMP_DIR, diff.path)
              await this.ensureDirectoryExists(path.dirname(targetPath))
              await fs.copyFile(tempFilePath, targetPath)
              
            } else if (diff.action === 'delete') {
              // 删除文件
              try {
                await fs.unlink(targetPath)
              } catch (error) {
                // 文件可能已经不存在，忽略错误
                this.logWarning(`删除文件失败: ${targetPath}`, 'applyIncrementalUpdate')
              }
            }
          }

          // 更新应用清单
          await this.updateAppManifest(fileDiffs)
          
          // 清理临时文件
          await this.cleanupTempFiles()
          
          this.logInfo('增量更新应用成功', 'applyIncrementalUpdate')
          
        } catch (error) {
          // 更新失败，尝试回滚
          if (enableBackup) {
            this.logWarning('更新失败，开始回滚', 'applyIncrementalUpdate')
            await this.rollbackUpdate()
          }
          throw error
        }
      },
      'applyIncrementalUpdate',
      '应用增量更新失败'
    )
  }

  /**
   * 回滚更新
   */
  static async rollbackUpdate(): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        const backupManifestPath = path.join(this.BACKUP_DIR, this.MANIFEST_FILE)
        
        try {
          const backupManifest = JSON.parse(
            await fs.readFile(backupManifestPath, 'utf-8')
          ) as this.AppManifest

          const appPath = app.getAppPath()
          
          // 恢复备份文件
          for (const [filePath, fileInfo] of Object.entries(backupManifest.files)) {
            const backupFilePath = path.join(this.BACKUP_DIR, filePath)
            const targetPath = path.join(appPath, filePath)
            
            try {
              await fs.copyFile(backupFilePath, targetPath)
            } catch (error) {
              this.logWarning(`恢复文件失败: ${filePath}`, 'rollbackUpdate')
            }
          }

          // 恢复清单文件
          const appManifestPath = path.join(appPath, this.MANIFEST_FILE)
          await fs.copyFile(backupManifestPath, appManifestPath)
          
          this.logInfo('更新回滚成功', 'rollbackUpdate')
          
        } catch (error) {
          this.logError('回滚失败，可能需要重新安装应用', 'rollbackUpdate')
          throw error
        }
      },
      'rollbackUpdate',
      '回滚更新失败'
    )
  }

  /**
   * 生成应用清单文件
   */
  private static async generateAppManifest(): Promise<void> {
    const appPath = app.getAppPath()
    const manifest: this.AppManifest = {
      version: app.getVersion(),
      files: {},
      timestamp: Date.now()
    }

    // 递归扫描应用文件
    await this.scanDirectory(appPath, appPath, manifest.files)
    
    // 保存清单文件
    const manifestPath = path.join(appPath, this.MANIFEST_FILE)
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2))
  }

  /**
   * 扫描目录文件
   */
  private static async scanDirectory(
    dirPath: string, 
    basePath: string, 
    files: Record<string, any>
  ): Promise<void> {
    const entries = await fs.readdir(dirPath, { withFileTypes: true })
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name)
      const relativePath = path.relative(basePath, fullPath)
      
      if (entry.isDirectory()) {
        await this.scanDirectory(fullPath, basePath, files)
      } else if (entry.isFile()) {
        const stats = await fs.stat(fullPath)
        const hash = await this.calculateFileHash(fullPath)
        
        files[relativePath] = {
          hash,
          size: stats.size,
          path: relativePath
        }
      }
    }
  }

  /**
   * 计算文件哈希值
   */
  private static async calculateFileHash(filePath: string): Promise<string> {
    const fileBuffer = await fs.readFile(filePath)
    return crypto.createHash('sha256').update(fileBuffer).digest('hex')
  }

  /**
   * 确保目录存在
   */
  private static async ensureDirectories(): Promise<void> {
    await this.ensureDirectoryExists(this.BACKUP_DIR)
    await this.ensureDirectoryExists(this.TEMP_DIR)
  }

  /**
   * 确保单个目录存在
   */
  private static async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath)
    } catch {
      await fs.mkdir(dirPath, { recursive: true })
    }
  }

  /**
   * 下载文件
   */
  private static async downloadFile(url: string, filePath: string): Promise<void> {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }
    
    const buffer = await response.arrayBuffer()
    await fs.writeFile(filePath, Buffer.from(buffer))
  }

  /**
   * 加载应用清单
   */
  private static async loadAppManifest(): Promise<this.AppManifest | null> {
    try {
      const manifestPath = path.join(app.getAppPath(), this.MANIFEST_FILE)
      const content = await fs.readFile(manifestPath, 'utf-8')
      return JSON.parse(content)
    } catch {
      return null
    }
  }

  /**
   * 更新应用清单
   */
  private static async updateAppManifest(fileDiffs: FileDiff[]): Promise<void> {
    const manifest = await this.loadAppManifest()
    if (!manifest) return

    // 更新清单中的文件信息
    for (const diff of fileDiffs) {
      if (diff.action === 'add' || diff.action === 'modify') {
        manifest.files[diff.path] = {
          hash: diff.hash,
          size: diff.size,
          path: diff.path
        }
      } else if (diff.action === 'delete') {
        delete manifest.files[diff.path]
      }
    }

    manifest.timestamp = Date.now()
    
    const manifestPath = path.join(app.getAppPath(), this.MANIFEST_FILE)
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2))
  }

  /**
   * 创建备份
   */
  private static async createBackup(fileDiffs: FileDiff[]): Promise<void> {
    const appPath = app.getAppPath()
    
    // 备份受影响的文件
    for (const diff of fileDiffs) {
      if (diff.action === 'modify' || diff.action === 'delete') {
        const sourcePath = path.join(appPath, diff.path)
        const backupPath = path.join(this.BACKUP_DIR, diff.path)
        
        try {
          await this.ensureDirectoryExists(path.dirname(backupPath))
          await fs.copyFile(sourcePath, backupPath)
        } catch (error) {
          this.logWarning(`备份文件失败: ${diff.path}`, 'createBackup')
        }
      }
    }

    // 备份当前清单文件
    const manifestPath = path.join(appPath, this.MANIFEST_FILE)
    const backupManifestPath = path.join(this.BACKUP_DIR, this.MANIFEST_FILE)
    await fs.copyFile(manifestPath, backupManifestPath)
  }

  /**
   * 清理临时文件
   */
  private static async cleanupTempFiles(): Promise<void> {
    try {
      await fs.rm(this.TEMP_DIR, { recursive: true, force: true })
      await this.ensureDirectoryExists(this.TEMP_DIR)
    } catch (error) {
      this.logWarning('清理临时文件失败', 'cleanupTempFiles')
    }
  }

  /**
   * 比较版本号
   */
  private static compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.replace(/^v/, '').split('.').map(Number)
    const v2Parts = version2.replace(/^v/, '').split('.').map(Number)
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length)
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0
      
      if (v1Part < v2Part) return -1
      if (v1Part > v2Part) return 1
    }
    
    return 0
  }
}
