import { app } from 'electron'
import pkg from 'electron-updater'
import { BaseService } from './base-service'
import type { ServiceResult } from './base-service'
import type { 
  UpdateInfo, 
  VersionApiResponse, 
  VersionCheckConfig,
  ExtendedVersionApiResponse,
  UpdateType 
} from '../../types/update-service'
import { UpdateStatus } from '../../types/update-service'
import { mainIPC } from '../../ipc/main-ipc'

const { autoUpdater } = pkg

/**
 * 统一更新服务
 * 整合 electron-updater 和 JSON API 更新，避免功能重复
 */
export class UnifiedUpdateService extends BaseService {
  protected static serviceName = 'UnifiedUpdateService'
  
  // 更新配置
  private static updateConfig = {
    // 更新源优先级：'electron-updater' | 'json-api' | 'hybrid'
    primarySource: 'hybrid' as 'electron-updater' | 'json-api' | 'hybrid',
    
    // JSON API 配置
    jsonApiConfig: {
      enabled: true,
      url: 'https://api.example.com/version',
      timeout: 10000,
      headers: {}
    } as VersionCheckConfig & { enabled: boolean },
    
    // electron-updater 配置
    electronUpdaterConfig: {
      enabled: true,
      url: 'https://example.com/auto-updates',
      channel: 'latest'
    },
    
    // 智能策略配置
    smartStrategy: {
      enabled: true,
      preferIncremental: true,
      fallbackToFull: true
    }
  }

  private static currentUpdateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE }
  private static isInitialized = false

  /**
   * 初始化统一更新服务
   */
  static initialize(config?: Partial<typeof UnifiedUpdateService.updateConfig>): void {
    if (this.isInitialized) return

    // 合并配置
    if (config) {
      this.updateConfig = { ...this.updateConfig, ...config }
    }

    // 根据配置初始化相应的服务
    if (this.updateConfig.electronUpdaterConfig.enabled) {
      this.setupElectronUpdater()
    }

    this.isInitialized = true
    this.logInfo('统一更新服务初始化完成', 'initialize')
  }

  /**
   * 统一检查更新入口
   * 根据配置自动选择最优的检查方式
   */
  static async checkForUpdates(): Promise<ServiceResult<{
    hasUpdate: boolean
    source: 'electron-updater' | 'json-api'
    updateInfo: UpdateInfo
    recommendation?: string
  }>> {
    return this.safeExecute(
      async () => {
        this.logInfo('开始统一更新检查', 'checkForUpdates')

        let result: any = null
        let source: 'electron-updater' | 'json-api' = 'electron-updater'

        switch (this.updateConfig.primarySource) {
          case 'electron-updater':
            result = await this.checkWithElectronUpdater()
            source = 'electron-updater'
            break

          case 'json-api':
            result = await this.checkWithJsonApi()
            source = 'json-api'
            break

          case 'hybrid':
            result = await this.checkWithHybridStrategy()
            source = result.source
            break
        }

        return {
          hasUpdate: result.hasUpdate,
          source,
          updateInfo: result.updateInfo,
          recommendation: result.recommendation
        }
      },
      'checkForUpdates',
      '统一更新检查失败'
    )
  }

  /**
   * 混合策略检查更新
   * 优先使用 JSON API，失败时回退到 electron-updater
   */
  private static async checkWithHybridStrategy(): Promise<{
    hasUpdate: boolean
    source: 'electron-updater' | 'json-api'
    updateInfo: UpdateInfo
    recommendation?: string
  }> {
    // 1. 首先尝试 JSON API（更灵活，支持增量更新）
    if (this.updateConfig.jsonApiConfig.enabled) {
      try {
        const jsonResult = await this.checkWithJsonApi()
        if (jsonResult.hasUpdate) {
          return {
            ...jsonResult,
            source: 'json-api',
            recommendation: 'JSON API检查成功，支持智能更新策略'
          }
        }
      } catch (error) {
        this.logWarning(`JSON API检查失败，回退到electron-updater: ${error}`, 'checkWithHybridStrategy')
      }
    }

    // 2. 回退到 electron-updater（稳定可靠）
    if (this.updateConfig.electronUpdaterConfig.enabled) {
      const electronResult = await this.checkWithElectronUpdater()
      return {
        ...electronResult,
        source: 'electron-updater',
        recommendation: electronResult.hasUpdate 
          ? 'electron-updater检查成功，将使用全量更新'
          : '当前已是最新版本'
      }
    }

    throw new Error('所有更新源都不可用')
  }

  /**
   * 使用 JSON API 检查更新
   */
  private static async checkWithJsonApi(): Promise<{
    hasUpdate: boolean
    updateInfo: UpdateInfo
  }> {
    const response = await this.fetchVersionFromJsonApi()
    
    if (!response.success || !response.data) {
      return {
        hasUpdate: false,
        updateInfo: { status: UpdateStatus.NOT_AVAILABLE }
      }
    }

    const currentVersion = app.getVersion()
    const latestVersion = response.data.version

    if (this.compareVersions(currentVersion, latestVersion) < 0) {
      const updateInfo: UpdateInfo = {
        status: UpdateStatus.AVAILABLE,
        version: latestVersion,
        releaseNotes: response.data.releaseNotes
      }

      this.currentUpdateInfo = updateInfo
      this.notifyRenderer()

      return {
        hasUpdate: true,
        updateInfo
      }
    }

    return {
      hasUpdate: false,
      updateInfo: { status: UpdateStatus.NOT_AVAILABLE }
    }
  }

  /**
   * 使用 electron-updater 检查更新
   */
  private static async checkWithElectronUpdater(): Promise<{
    hasUpdate: boolean
    updateInfo: UpdateInfo
  }> {
    return new Promise((resolve) => {
      // 设置一次性监听器
      const onUpdateAvailable = (info: any) => {
        autoUpdater.removeListener('update-available', onUpdateAvailable)
        autoUpdater.removeListener('update-not-available', onUpdateNotAvailable)
        autoUpdater.removeListener('error', onError)

        const updateInfo: UpdateInfo = {
          status: UpdateStatus.AVAILABLE,
          version: info.version,
          releaseNotes: info.releaseNotes as string
        }

        this.currentUpdateInfo = updateInfo
        this.notifyRenderer()

        resolve({
          hasUpdate: true,
          updateInfo
        })
      }

      const onUpdateNotAvailable = () => {
        autoUpdater.removeListener('update-available', onUpdateAvailable)
        autoUpdater.removeListener('update-not-available', onUpdateNotAvailable)
        autoUpdater.removeListener('error', onError)

        resolve({
          hasUpdate: false,
          updateInfo: { status: UpdateStatus.NOT_AVAILABLE }
        })
      }

      const onError = (error: Error) => {
        autoUpdater.removeListener('update-available', onUpdateAvailable)
        autoUpdater.removeListener('update-not-available', onUpdateNotAvailable)
        autoUpdater.removeListener('error', onError)

        throw error
      }

      autoUpdater.once('update-available', onUpdateAvailable)
      autoUpdater.once('update-not-available', onUpdateNotAvailable)
      autoUpdater.once('error', onError)

      // 开始检查
      autoUpdater.checkForUpdates()
    })
  }

  /**
   * 统一下载更新
   * 根据检查结果选择合适的下载方式
   */
  static async downloadUpdate(source?: 'electron-updater' | 'json-api'): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (!source) {
          // 根据当前状态推断下载方式
          source = this.currentUpdateInfo.version ? 'electron-updater' : 'json-api'
        }

        if (source === 'electron-updater') {
          await this.downloadWithElectronUpdater()
        } else {
          await this.downloadWithJsonApi()
        }
      },
      'downloadUpdate',
      '下载更新失败'
    )
  }

  /**
   * 使用 electron-updater 下载
   */
  private static async downloadWithElectronUpdater(): Promise<void> {
    return new Promise((resolve, reject) => {
      const onDownloaded = () => {
        autoUpdater.removeListener('update-downloaded', onDownloaded)
        autoUpdater.removeListener('error', onError)
        resolve()
      }

      const onError = (error: Error) => {
        autoUpdater.removeListener('update-downloaded', onDownloaded)
        autoUpdater.removeListener('error', onError)
        reject(error)
      }

      autoUpdater.once('update-downloaded', onDownloaded)
      autoUpdater.once('error', onError)

      autoUpdater.downloadUpdate()
    })
  }

  /**
   * 使用 JSON API 下载（需要实现自定义下载逻辑）
   */
  private static async downloadWithJsonApi(): Promise<void> {
    // 这里需要实现基于 JSON API 的下载逻辑
    // 可以集成之前实现的增量更新服务
    throw new Error('JSON API 下载功能待实现')
  }

  /**
   * 统一安装更新
   */
  static installUpdate(): ServiceResult<void> {
    try {
      if (this.currentUpdateInfo.status !== UpdateStatus.DOWNLOADED) {
        return this.createErrorResult('更新尚未下载完成')
      }

      this.currentUpdateInfo.status = UpdateStatus.INSTALLING
      this.notifyRenderer()

      // 目前只支持 electron-updater 的安装
      autoUpdater.quitAndInstall()

      return this.createSuccessResult()
    } catch (error) {
      return this.createErrorResult(error instanceof Error ? error.message : '安装更新失败')
    }
  }

  /**
   * 获取更新配置
   */
  static getUpdateConfig() {
    return { ...this.updateConfig }
  }

  /**
   * 更新配置
   */
  static updateConfig(newConfig: Partial<typeof UnifiedUpdateService.updateConfig>): void {
    this.updateConfig = { ...this.updateConfig, ...newConfig }
    this.logInfo('更新配置已更新', 'updateConfig')
  }

  /**
   * 获取当前更新信息
   */
  static getUpdateInfo(): UpdateInfo {
    return { ...this.currentUpdateInfo }
  }

  /**
   * 从 JSON API 获取版本信息
   */
  private static async fetchVersionFromJsonApi(): Promise<VersionApiResponse> {
    const config = this.updateConfig.jsonApiConfig
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), config.timeout)

    try {
      const response = await fetch(config.url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': `${app.getName()}/${app.getVersion()}`,
          ...config.headers,
        },
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  /**
   * 设置 electron-updater
   */
  private static setupElectronUpdater(): void {
    const config = this.updateConfig.electronUpdaterConfig

    autoUpdater.setFeedURL({
      provider: 'generic',
      url: config.url,
      channel: config.channel,
    })

    // 设置基本的事件监听器
    autoUpdater.on('download-progress', (progressObj) => {
      this.currentUpdateInfo = {
        status: UpdateStatus.DOWNLOADING,
        progress: {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred,
        },
      }
      this.notifyRenderer()
    })

    autoUpdater.on('update-downloaded', (info) => {
      this.currentUpdateInfo = {
        status: UpdateStatus.DOWNLOADED,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
    })
  }

  /**
   * 通知渲染进程
   */
  private static notifyRenderer(): void {
    try {
      mainIPC.emit('update-status', this.currentUpdateInfo)
    } catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'notifyRenderer')
    }
  }

  /**
   * 比较版本号
   */
  private static compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.replace(/^v/, '').split('.').map(Number)
    const v2Parts = version2.replace(/^v/, '').split('.').map(Number)
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length)
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0
      
      if (v1Part < v2Part) return -1
      if (v1Part > v2Part) return 1
    }
    
    return 0
  }
}
