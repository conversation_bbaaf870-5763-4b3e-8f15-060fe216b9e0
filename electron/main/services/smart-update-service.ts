import { app } from 'electron'
import { BaseService } from './base-service'
import { UpdateService } from './update-service'
import { IncrementalUpdateService } from './incremental-update-service'
import type { ServiceResult } from './base-service'
import type { 
  ExtendedVersionApiResponse, 
  UpdateType, 
  IncrementalUpdateInfo,
  VersionCheckConfig 
} from '../../types/update-service'

/**
 * 智能更新策略服务
 * 自动选择最优的更新方式（增量 vs 全量）
 */
export class SmartUpdateService extends BaseService {
  protected static serviceName = 'SmartUpdateService'
  
  /**
   * 更新策略配置
   */
  private static readonly UPDATE_STRATEGY = {
    // 增量更新阈值（MB）- 如果更新大小小于此值，优先使用增量更新
    INCREMENTAL_THRESHOLD: 50,
    
    // 网络速度阈值（KB/s）- 如果网络速度低于此值，优先使用增量更新
    SLOW_NETWORK_THRESHOLD: 500,
    
    // 磁盘空间阈值（MB）- 如果可用空间小于此值，优先使用增量更新
    LOW_DISK_SPACE_THRESHOLD: 1000,
    
    // 增量更新最大文件数量 - 如果变更文件数量超过此值，使用全量更新
    MAX_INCREMENTAL_FILES: 1000,
  }

  /**
   * 智能检查更新
   * 自动选择最优的更新策略
   */
  static async smartCheckForUpdates(config: VersionCheckConfig): Promise<ServiceResult<{
    hasUpdate: boolean
    updateType: UpdateType
    updateInfo: ExtendedVersionApiResponse
    recommendation: string
  }>> {
    return this.safeExecute(
      async () => {
        this.logInfo('开始智能更新检查', 'smartCheckForUpdates')
        
        // 1. 检查版本更新
        const versionResult = await UpdateService.checkVersionFromAPI(config)
        if (!versionResult.success || !versionResult.data) {
          throw new Error('版本检查失败')
        }

        const apiResponse = versionResult.data as ExtendedVersionApiResponse
        
        if (!apiResponse.success || !apiResponse.data) {
          return {
            hasUpdate: false,
            updateType: UpdateType.FULL,
            updateInfo: apiResponse,
            recommendation: '当前已是最新版本'
          }
        }

        const currentVersion = app.getVersion()
        const targetVersion = apiResponse.data.version
        
        // 2. 分析更新策略
        const strategy = await this.analyzeUpdateStrategy(
          currentVersion,
          targetVersion,
          apiResponse
        )

        this.logInfo(`推荐更新策略: ${strategy.updateType} - ${strategy.recommendation}`, 'smartCheckForUpdates')
        
        return {
          hasUpdate: true,
          updateType: strategy.updateType,
          updateInfo: apiResponse,
          recommendation: strategy.recommendation
        }
      },
      'smartCheckForUpdates',
      '智能更新检查失败'
    )
  }

  /**
   * 分析更新策略
   */
  private static async analyzeUpdateStrategy(
    currentVersion: string,
    targetVersion: string,
    apiResponse: ExtendedVersionApiResponse
  ): Promise<{
    updateType: UpdateType
    recommendation: string
    factors: string[]
  }> {
    const factors: string[] = []
    let score = 0 // 增量更新得分，分数越高越倾向于增量更新
    
    // 1. 检查是否支持增量更新
    const incrementalSupport = await IncrementalUpdateService.checkIncrementalUpdateSupport(
      currentVersion,
      targetVersion,
      apiResponse
    )
    
    if (!incrementalSupport.success || !incrementalSupport.data) {
      return {
        updateType: UpdateType.FULL,
        recommendation: '服务器不支持增量更新，使用全量更新',
        factors: ['不支持增量更新']
      }
    }

    // 2. 分析更新大小
    const incrementalInfo = apiResponse.data?.incrementalUpdate
    if (incrementalInfo) {
      const incrementalSizeMB = incrementalInfo.totalSize / (1024 * 1024)
      const fullUpdateSizeMB = this.estimateFullUpdateSize()
      
      if (incrementalSizeMB < this.UPDATE_STRATEGY.INCREMENTAL_THRESHOLD) {
        score += 30
        factors.push(`增量更新大小较小 (${incrementalSizeMB.toFixed(1)}MB)`)
      }
      
      if (incrementalSizeMB < fullUpdateSizeMB * 0.3) {
        score += 20
        factors.push(`增量更新比全量更新小${((1 - incrementalSizeMB / fullUpdateSizeMB) * 100).toFixed(1)}%`)
      }

      // 检查文件变更数量
      if (incrementalInfo.fileDiffs.length > this.UPDATE_STRATEGY.MAX_INCREMENTAL_FILES) {
        score -= 40
        factors.push(`变更文件过多 (${incrementalInfo.fileDiffs.length}个)`)
      }
    }

    // 3. 分析网络状况
    const networkSpeed = await this.estimateNetworkSpeed()
    if (networkSpeed < this.UPDATE_STRATEGY.SLOW_NETWORK_THRESHOLD) {
      score += 25
      factors.push(`网络速度较慢 (${networkSpeed}KB/s)`)
    }

    // 4. 分析磁盘空间
    const diskSpace = await this.getAvailableDiskSpace()
    if (diskSpace < this.UPDATE_STRATEGY.LOW_DISK_SPACE_THRESHOLD) {
      score += 15
      factors.push(`磁盘空间不足 (${diskSpace}MB可用)`)
    }

    // 5. 分析版本跨度
    const versionGap = this.calculateVersionGap(currentVersion, targetVersion)
    if (versionGap > 2) {
      score -= 20
      factors.push(`版本跨度较大 (跨越${versionGap}个版本)`)
    }

    // 6. 分析更新类型
    const updateType = apiResponse.data?.updateType
    if (updateType === UpdateType.HOTFIX || updateType === UpdateType.PATCH) {
      score += 25
      factors.push(`${updateType}类型更新适合增量更新`)
    }

    // 7. 分析平台特性
    if (process.platform === 'darwin') {
      score -= 10
      factors.push('macOS平台增量更新支持有限')
    }

    // 决策逻辑
    let finalUpdateType: UpdateType
    let recommendation: string

    if (score >= 50) {
      finalUpdateType = UpdateType.INCREMENTAL
      recommendation = `推荐使用增量更新 (评分: ${score})`
    } else if (score >= 20) {
      finalUpdateType = UpdateType.INCREMENTAL
      recommendation = `可以使用增量更新，但全量更新可能更稳定 (评分: ${score})`
    } else {
      finalUpdateType = UpdateType.FULL
      recommendation = `推荐使用全量更新 (评分: ${score})`
    }

    return {
      updateType: finalUpdateType,
      recommendation,
      factors
    }
  }

  /**
   * 执行智能更新
   */
  static async executeSmartUpdate(
    updateType: UpdateType,
    apiResponse: ExtendedVersionApiResponse,
    onProgress?: (progress: { percent: number; stage: string; details?: string }) => void
  ): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (updateType === UpdateType.INCREMENTAL) {
          await this.executeIncrementalUpdate(apiResponse, onProgress)
        } else {
          await this.executeFullUpdate(onProgress)
        }
      },
      'executeSmartUpdate',
      '智能更新执行失败'
    )
  }

  /**
   * 执行增量更新
   */
  private static async executeIncrementalUpdate(
    apiResponse: ExtendedVersionApiResponse,
    onProgress?: (progress: { percent: number; stage: string; details?: string }) => void
  ): Promise<void> {
    const incrementalInfo = apiResponse.data?.incrementalUpdate
    if (!incrementalInfo) {
      throw new Error('缺少增量更新信息')
    }

    // 阶段1: 计算差异
    onProgress?.({ percent: 10, stage: '计算文件差异' })
    const diffResult = await IncrementalUpdateService.calculateUpdateDiff(incrementalInfo)
    if (!diffResult.success || !diffResult.data) {
      throw new Error('计算文件差异失败')
    }

    // 阶段2: 下载文件
    onProgress?.({ percent: 20, stage: '下载更新文件' })
    await IncrementalUpdateService.downloadIncrementalFiles(
      diffResult.data,
      (downloadProgress) => {
        onProgress?.({
          percent: 20 + (downloadProgress.percent * 0.6),
          stage: '下载更新文件',
          details: `${downloadProgress.current}/${downloadProgress.total} 文件`
        })
      }
    )

    // 阶段3: 应用更新
    onProgress?.({ percent: 80, stage: '应用更新' })
    const applyResult = await IncrementalUpdateService.applyIncrementalUpdate(
      diffResult.data,
      true // 启用备份
    )
    
    if (!applyResult.success) {
      throw new Error('应用增量更新失败')
    }

    onProgress?.({ percent: 100, stage: '更新完成' })
  }

  /**
   * 执行全量更新
   */
  private static async executeFullUpdate(
    onProgress?: (progress: { percent: number; stage: string; details?: string }) => void
  ): Promise<void> {
    onProgress?.({ percent: 0, stage: '开始全量更新' })
    
    // 使用传统的electron-updater进行全量更新
    const checkResult = await UpdateService.checkForUpdates()
    if (!checkResult.success) {
      throw new Error('检查更新失败')
    }

    onProgress?.({ percent: 20, stage: '下载更新包' })
    const downloadResult = await UpdateService.downloadUpdate()
    if (!downloadResult.success) {
      throw new Error('下载更新失败')
    }

    onProgress?.({ percent: 100, stage: '准备安装' })
  }

  /**
   * 估算网络速度
   */
  private static async estimateNetworkSpeed(): Promise<number> {
    try {
      const startTime = Date.now()
      const response = await fetch('https://httpbin.org/bytes/1024', { 
        method: 'GET',
        cache: 'no-cache'
      })
      
      if (response.ok) {
        await response.arrayBuffer()
        const endTime = Date.now()
        const duration = (endTime - startTime) / 1000 // 秒
        const speed = 1024 / duration // bytes/s
        return speed / 1024 // KB/s
      }
    } catch (error) {
      this.logWarning('网络速度测试失败', 'estimateNetworkSpeed')
    }
    
    // 默认返回中等速度
    return 1000 // KB/s
  }

  /**
   * 获取可用磁盘空间
   */
  private static async getAvailableDiskSpace(): Promise<number> {
    try {
      const fs = await import('fs/promises')
      const stats = await fs.statfs(app.getPath('userData'))
      return (stats.bavail * stats.bsize) / (1024 * 1024) // MB
    } catch (error) {
      this.logWarning('获取磁盘空间失败', 'getAvailableDiskSpace')
      return 10000 // 默认返回10GB
    }
  }

  /**
   * 估算全量更新大小
   */
  private static estimateFullUpdateSize(): number {
    // 根据应用类型估算，这里返回一个默认值
    // 实际项目中可以从配置文件或API获取
    return 100 // MB
  }

  /**
   * 计算版本跨度
   */
  private static calculateVersionGap(currentVersion: string, targetVersion: string): number {
    try {
      const current = currentVersion.replace(/^v/, '').split('.').map(Number)
      const target = targetVersion.replace(/^v/, '').split('.').map(Number)
      
      // 简单计算主版本号差异
      return Math.abs(target[0] - current[0]) + Math.abs(target[1] - current[1])
    } catch {
      return 1
    }
  }

  /**
   * 获取更新建议
   */
  static async getUpdateRecommendation(
    currentVersion: string,
    apiResponse: ExtendedVersionApiResponse
  ): Promise<ServiceResult<{
    recommended: UpdateType
    reasons: string[]
    alternatives: Array<{
      type: UpdateType
      pros: string[]
      cons: string[]
    }>
  }>> {
    return this.safeExecute(
      async () => {
        const strategy = await this.analyzeUpdateStrategy(
          currentVersion,
          apiResponse.data?.version || '',
          apiResponse
        )

        const alternatives = [
          {
            type: UpdateType.INCREMENTAL,
            pros: [
              '下载量小，节省带宽',
              '更新速度快',
              '支持回滚',
              '对磁盘空间要求低'
            ],
            cons: [
              '可能存在兼容性问题',
              '需要服务器支持',
              '复杂度较高'
            ]
          },
          {
            type: UpdateType.FULL,
            pros: [
              '稳定可靠',
              '兼容性好',
              '实现简单',
              '支持所有平台'
            ],
            cons: [
              '下载量大',
              '更新时间长',
              '占用更多带宽和存储'
            ]
          }
        ]

        return {
          recommended: strategy.updateType,
          reasons: strategy.factors,
          alternatives
        }
      },
      'getUpdateRecommendation',
      '获取更新建议失败'
    )
  }
}
