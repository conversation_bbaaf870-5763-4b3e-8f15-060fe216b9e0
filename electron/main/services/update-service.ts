import { app, dialog } from 'electron'
import pkg from 'electron-updater'
import axios from 'axios'
import type { AxiosError, AxiosRequestConfig } from 'axios'
import type { BrowserWindow } from 'electron'
import type { UpdateInfo, VersionApiResponse, VersionCheckConfig } from '../../types/update-service'
import { UpdateStatus } from '../../types/update-service'
import { mainIPC } from '../../ipc/main-ipc'
import { BaseService } from './base-service'
import type { ServiceResult } from './base-service'

const { autoUpdater } = pkg

/**
 * 创建版本检查专用的axios实例
 */
function createVersionCheckAxios() {
  return axios.create({
    timeout: 30000, // 30秒超时
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    validateStatus: status => status >= 200 && status < 300,
  })
}

/**
 * 更新服务类
 * 负责处理应用程序的自动更新功能
 */
export class UpdateService extends BaseService {
  protected static serviceName = 'UpdateService' // 服务名称，用于日志记录和标识
  private static mainWindow: BrowserWindow | null = null // 主窗口引用，用于向渲染进程发送更新状态
  private static isChecking = false // 标记当前是否正在检查更新
  private static isDownloading = false // 标记当前是否正在下载更新
  private static isInitialized = false
  private static updateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE } // 当前更新信息，初始状态为"无可用更新"

  /**
   * 初始化更新服务
   */
  static initialize(): void {
    if (this.isInitialized) {
      this.logWarning('UpdateService 已经初始化，跳过重复初始化', 'initialize')
      return
    }
    this.setupAutoUpdater()
    this.isInitialized = true
    this.logInfo('UpdateService 初始化完成', 'initialize')
  }

  /**
   * 设置主窗口引用
   */
  static setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window

    // 确保服务已初始化
    if (!this.isInitialized) {
      this.initialize()
    }

    // 在生产环境中延迟检查更新
    if (process.env.NODE_ENV === 'production') {
      // 延迟15秒后检查更新，给应用充分的启动时间
      setTimeout(() => {
        this.checkForUpdates() // 静默检查
      }, 10 * 1000) // 10秒延迟
    }

    this.logInfo('主窗口已设置，更新服务准备就绪', 'setMainWindow')
  }

  /**
   * 配置 autoUpdater
   */
  private static setupAutoUpdater(): void {
    // 配置更新服务器
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: 'https://example.com/auto-updates', // 替换为您的更新服务器地址
      channel: 'latest',
    })

    // 根据平台配置更新策略
    if (process.platform === 'darwin') {
      // macOS 只使用全量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }
    else {
      // Windows/Linux 支持增量更新
      autoUpdater.allowDowngrade = false
      autoUpdater.allowPrerelease = false
    }

    // 配置日志
    autoUpdater.logger = {
      info: message => this.logInfo(message, 'autoUpdater'),
      warn: message => this.logWarning(message, 'autoUpdater'),
      error: message => this.logError(message, 'autoUpdater'),
      debug: message => this.logInfo(`[DEBUG] ${message}`, 'autoUpdater'),
    }

    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private static setupEventListeners(): void {
    // 检查更新开始
    autoUpdater.on('checking-for-update', () => {
      this.isChecking = true
      this.updateInfo = { status: UpdateStatus.CHECKING }
      this.notifyRenderer()
      this.logInfo('开始检查更新', 'setupEventListeners')
    })

    // 发现可用更新
    autoUpdater.on('update-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.AVAILABLE,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo(`发现新版本: ${info.version}`, 'setupEventListeners')
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', (info) => {
      this.isChecking = false
      this.updateInfo = {
        status: UpdateStatus.NOT_AVAILABLE,
        version: info.version,
      }
      this.notifyRenderer()
      this.logInfo('当前已是最新版本', 'setupEventListeners')
    })

    // 下载进度
    autoUpdater.on('download-progress', (progressObj) => {
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADING,
        progress: {
          percent: progressObj.percent,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred,
        },
      }
      this.notifyRenderer()
    })

    // 下载完成
    autoUpdater.on('update-downloaded', (info) => {
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.DOWNLOADED,
        version: info.version,
        releaseNotes: info.releaseNotes as string,
      }
      this.notifyRenderer()
      this.logInfo('更新下载完成', 'setupEventListeners')

      // 询问用户是否立即安装
      this.promptInstallUpdate()
    })

    // 更新错误
    autoUpdater.on('error', (error) => {
      this.isChecking = false
      this.isDownloading = false
      this.updateInfo = {
        status: UpdateStatus.ERROR,
        error: error.message,
      }
      this.notifyRenderer()
      this.logError(`更新错误: ${error.message}`, 'setupEventListeners')
    })
  }

  /**
   * 检查更新
   */
  static async checkForUpdates(): Promise<ServiceResult<void>> {
    // 确保服务已初始化
    if (!this.isInitialized) {
      this.initialize()
    }
    return this.safeExecute(
      async () => {
        if (this.isChecking) {
          this.logWarning('正在检查更新中，请稍候', 'checkForUpdates')
          return
        }

        await autoUpdater.checkForUpdatesAndNotify()
      },
      'checkForUpdates',
      '检查更新失败',
    )
  }

  /**
   * 下载更新
   */
  static async downloadUpdate(): Promise<ServiceResult<void>> {
    return this.safeExecute(
      async () => {
        if (this.isDownloading) {
          this.logWarning('正在下载更新中', 'downloadUpdate')
          return
        }

        if (this.updateInfo.status !== UpdateStatus.AVAILABLE) {
          this.logWarning('没有可用的更新', 'downloadUpdate')
          return
        }

        this.isDownloading = true
        await autoUpdater.downloadUpdate()
      },
      'downloadUpdate',
      '下载更新失败',
    )
  }

  /**
   * 安装更新并重启应用
   */
  static installUpdate(): ServiceResult<void> {
    try {
      if (this.updateInfo.status !== UpdateStatus.DOWNLOADED) {
        this.logWarning('更新尚未下载完成', 'installUpdate')
        return this.createErrorResult('更新尚未下载完成')
      }

      this.updateInfo.status = UpdateStatus.INSTALLING
      this.notifyRenderer()

      this.logInfo('开始安装更新并重启应用', 'installUpdate')
      autoUpdater.quitAndInstall()

      return this.createSuccessResult()
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'installUpdate')
      return this.createErrorResult(error instanceof Error ? error.message : '安装更新失败')
    }
  }

  /**
   * 提示用户安装更新
   */
  private static async promptInstallUpdate(): Promise<void> {
    if (!this.mainWindow)
      return

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新下载完成',
      message: '新版本已下载完成，是否立即重启应用进行安装？',
      detail: '您可以选择稍后手动重启应用来完成更新。',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1,
    })

    if (result.response === 0) {
      this.installUpdate()
    }
  }

  /**
   * 通知渲染进程更新状态
   */
  private static notifyRenderer(): void {
    try {
      // 使用 mainIPC 发送事件到所有渲染进程
      mainIPC.emit('update-status', this.updateInfo)
      this.logInfo('更新状态已通知到渲染进程', 'notifyRenderer')
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'notifyRenderer')
    }
  }

  /**
   * 获取当前更新信息
   */
  static getUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 获取当前应用版本
   */
  static getCurrentVersion(): string {
    return app.getVersion()
  }

  /**
   * 通过JSON API检查版本更新
   * @param config 版本检查配置
   */
  static async checkVersionFromAPI(config: VersionCheckConfig): Promise<ServiceResult<VersionApiResponse>> {
    return this.safeExecute(
      async () => {
        this.logInfo(`开始从API检查版本: ${config.apiUrl}`, 'checkVersionFromAPI')

        // 创建axios实例
        const axiosInstance = createVersionCheckAxios()

        // 构建请求配置
        const requestConfig: AxiosRequestConfig = {
          method: 'GET',
          url: config.apiUrl,
          timeout: config.timeout || 10000,
          headers: {
            'User-Agent': `${app.getName()}/${app.getVersion()}`,
            ...config.headers,
          },
        }

        try {
          const response = await axiosInstance.request<VersionApiResponse>(requestConfig)
          const data = response.data

          this.logInfo(`API响应: ${JSON.stringify(data)}`, 'checkVersionFromAPI')

          // 处理版本比较逻辑
          if (data.success && data.data) {
            const currentVersion = this.getCurrentVersion()
            const latestVersion = data.data.version

            if (this.compareVersions(currentVersion, latestVersion) < 0) {
              // 有新版本可用
              this.updateInfo = {
                status: UpdateStatus.AVAILABLE,
                version: latestVersion,
                releaseNotes: data.data.releaseNotes,
              }
              this.notifyRenderer()
              this.logInfo(`发现新版本: ${latestVersion} (当前: ${currentVersion})`, 'checkVersionFromAPI')
            }
            else {
              // 已是最新版本
              this.updateInfo = {
                status: UpdateStatus.NOT_AVAILABLE,
                version: currentVersion,
              }
              this.notifyRenderer()
              this.logInfo('当前已是最新版本', 'checkVersionFromAPI')
            }
          }

          return data
        }
        catch (error) {
          // 处理axios错误
          if (axios.isAxiosError(error)) {
            const axiosError = error as AxiosError
            if (axiosError.code === 'ECONNABORTED') {
              throw new Error('请求超时')
            }
            if (axiosError.response) {
              throw new Error(`HTTP ${axiosError.response.status}: ${axiosError.response.statusText}`)
            }
            if (axiosError.request) {
              throw new Error('网络连接失败')
            }
          }
          throw error
        }
      },
      'checkVersionFromAPI',
      '从API检查版本失败',
    )
  }

  /**
   * 比较版本号
   * @param version1 版本1
   * @param version2 版本2
   * @returns -1: version1 < version2, 0: version1 = version2, 1: version1 > version2
   */
  private static compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.replace(/^v/, '').split('.').map(Number)
    const v2Parts = version2.replace(/^v/, '').split('.').map(Number)

    const maxLength = Math.max(v1Parts.length, v2Parts.length)

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0

      if (v1Part < v2Part)
        return -1
      if (v1Part > v2Part)
        return 1
    }

    return 0
  }

  /**
   * 清理资源
   */
  static cleanup(): void {
    autoUpdater.removeAllListeners()
    this.mainWindow = null
    this.logInfo('UpdateService 资源清理完成', 'cleanup')
  }
}
