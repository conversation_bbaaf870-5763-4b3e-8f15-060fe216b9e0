import type { UpdateInfo } from '../../types/update-service'
import { UpdateStatus } from '../../types/update-service'
import { BaseRendererService } from './base-service'

/**
 * 服务结果类型定义
 */
interface ServiceResult<T = void> {
  success: boolean
  data?: T
  error?: string
}

/**
 * 渲染进程更新服务
 * 负责与主进程的更新功能进行通信和UI状态管理
 */
export class UpdateService extends BaseRendererService {
  protected static serviceName = 'UpdateService'
  private static updateInfo: UpdateInfo = { status: UpdateStatus.NOT_AVAILABLE }
  private static listeners: Array<(info: UpdateInfo) => void> = []
  private static isInitialized = false

  /**
   * 初始化更新服务
   * 注册更新状态监听器
   */
  static initialize(): void {
    if (this.isInitialized) {
      this.logDebug('更新服务已初始化，跳过重复初始化', 'initialize')
      return
    }

    this.logDebug('初始化渲染进程更新服务', 'initialize')

    try {
      const ipc = this.getIPC()

      // 监听主进程发送的更新状态
      ipc.on('update-status', (updateInfo: UpdateInfo) => {
        this.logDebug(`收到更新状态: ${updateInfo.status}`, 'initialize')
        this.updateInfo = updateInfo
        this.notifyListeners(updateInfo)
      })

      this.isInitialized = true
      this.logDebug('更新服务初始化完成', 'initialize')
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'initialize')
    }
  }

  /**
   * 检查更新
   */
  static async checkForUpdates(): Promise<ServiceResult<void>> {
    this.logDebug('开始检查更新', 'checkForUpdates')

    try {
      const result = await this.safeInvoke<null, ServiceResult<void>>('update:check', null)
      this.logDebug('检查更新请求发送成功', 'checkForUpdates')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'checkForUpdates')
      return {
        success: false,
        error: error instanceof Error ? error.message : '检查更新失败',
      }
    }
  }

  /**
   * 下载更新
   */
  static async downloadUpdate(): Promise<ServiceResult<void>> {
    this.logDebug('开始下载更新', 'downloadUpdate')

    try {
      const result = await this.safeInvoke<null, ServiceResult<void>>('update:download', null)
      this.logDebug('下载更新请求发送成功', 'downloadUpdate')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'downloadUpdate')
      return {
        success: false,
        error: error instanceof Error ? error.message : '下载更新失败',
      }
    }
  }

  /**
   * 安装更新
   */
  static async installUpdate(): Promise<ServiceResult<void>> {
    this.logDebug('开始安装更新', 'installUpdate')

    try {
      const result = await this.safeInvoke<null, ServiceResult<void>>('update:install', null)
      this.logDebug('安装更新请求发送成功', 'installUpdate')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'installUpdate')
      return {
        success: false,
        error: error instanceof Error ? error.message : '安装更新失败',
      }
    }
  }

  /**
   * 获取更新信息
   */
  static async getUpdateInfo(): Promise<UpdateInfo> {
    this.logDebug('获取更新信息', 'getUpdateInfo')

    try {
      const result = await this.safeInvoke<null, UpdateInfo>('update:getInfo', null)
      this.updateInfo = result
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getUpdateInfo')
      return this.updateInfo
    }
  }

  /**
   * 获取当前版本
   */
  static async getCurrentVersion(): Promise<string> {
    this.logDebug('获取当前版本', 'getCurrentVersion')

    try {
      const result = await this.safeInvoke<void, string>('update:getCurrentVersion')
      this.logDebug(`当前版本: ${result}`, 'getCurrentVersion')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'getCurrentVersion')
      return '未知版本'
    }
  }

  /**
   * 通过JSON API检查版本更新
   * @param config 版本检查配置
   */
  static async checkVersionFromAPI(): Promise<any> {
    try {
      const result = await this.safeInvoke<VersionCheckConfig, ServiceResult<VersionApiResponse>>('update:checkVersionFromAPI', config)
      this.logDebug('API版本检查请求发送成功', 'checkVersionFromAPI')
      return result
    }
    catch (error) {
      this.logError(error instanceof Error ? error : String(error), 'checkVersionFromAPI')
      return {
        success: false,
        error: error instanceof Error ? error.message : '从API检查版本失败',
      }
    }
  }

  /**
   * 添加更新状态监听器
   */
  static addUpdateListener(listener: (info: UpdateInfo) => void): void {
    this.listeners.push(listener)
    this.logDebug('添加更新状态监听器', 'addUpdateListener')
  }

  /**
   * 移除更新状态监听器
   */
  static removeUpdateListener(listener: (info: UpdateInfo) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
      this.logDebug('移除更新状态监听器', 'removeUpdateListener')
    }
  }

  /**
   * 通知所有监听器
   */
  private static notifyListeners(updateInfo: UpdateInfo): void {
    this.listeners.forEach((listener) => {
      try {
        listener(updateInfo)
      }
      catch (error) {
        this.logError(`监听器执行失败: ${error}`, 'notifyListeners')
      }
    })
  }

  /**
   * 获取当前缓存的更新信息
   */
  static getCachedUpdateInfo(): UpdateInfo {
    return { ...this.updateInfo }
  }

  /**
   * 清理资源
   */
  static cleanup(): void {
    this.listeners = []
    this.updateInfo = { status: UpdateStatus.NOT_AVAILABLE }
    this.logDebug('更新服务资源清理完成', 'cleanup')
  }
}
