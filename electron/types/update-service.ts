/**
 * 更新状态枚举
 */
export enum UpdateStatus {
  CHECKING = 'checking', // 正在检查更新
  AVAILABLE = 'available', // 有可用更新
  NOT_AVAILABLE = 'not-available', // 没有可用更新
  DOWNLOADING = 'downloading', // 正在下载更新
  DOWNLOADED = 'downloaded', // 更新已下载完成
  ERROR = 'error', // 更新过程中出现错误
  INSTALLING = 'installing', // 正在安装更新
}

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  status: UpdateStatus // 当前更新状态，使用前面定义的 UpdateStatus 枚举
  version?: string // 可选，更新版本号（当有可用更新或下载完成时提供）
  releaseNotes?: string // 可选，更新说明/发布日志（当有可用更新或下载完成时提供）
  progress?: { // 可选，下载进度信息（仅在下载过程中提供）
    percent: number // 下载进度百分比
    bytesPerSecond: number // 下载速度（字节/秒）
    total: number // 需要下载的总字节数
    transferred: number // 已下载的字节数
  }
  error?: string // 可选，错误信息（仅在更新出错时提供）
}

/**
 * JSON API 版本信息接口
 * 用于从服务器获取版本信息的JSON响应格式
 */
export interface VersionApiResponse {
  success: boolean // 请求是否成功
  data?: {
    version: string // 最新版本号
    releaseNotes?: string // 发布说明
    downloadUrl?: string // 下载链接
    publishedAt?: string // 发布时间
    isForced?: boolean // 是否强制更新
    minVersion?: string // 最低支持版本
  }
  error?: string // 错误信息
}

/**
 * 版本检查配置
 */
export interface VersionCheckConfig {
  apiUrl: string // API 接口地址
  timeout?: number // 请求超时时间（毫秒），默认 10000
  headers?: Record<string, string> // 自定义请求头
}

/**
 * 更新类型枚举
 */
export enum UpdateType {
  FULL = 'full', // 全量更新
  INCREMENTAL = 'incremental', // 增量更新
  PATCH = 'patch', // 补丁更新
  HOTFIX = 'hotfix', // 热修复更新
}

/**
 * 文件差异信息
 */
export interface FileDiff {
  path: string // 文件路径
  action: 'add' | 'modify' | 'delete' // 操作类型
  hash: string // 文件哈希值
  size: number // 文件大小
  downloadUrl?: string // 下载链接（新增或修改的文件）
}

/**
 * 增量更新信息
 */
export interface IncrementalUpdateInfo {
  fromVersion: string // 源版本
  toVersion: string // 目标版本
  updateType: UpdateType // 更新类型
  totalSize: number // 总下载大小
  fileDiffs: FileDiff[] // 文件差异列表
  checksum: string // 整体校验和
  rollbackInfo?: {
    enabled: boolean // 是否支持回滚
    backupPath?: string // 备份路径
  }
}

/**
 * 扩展的版本API响应
 */
export interface ExtendedVersionApiResponse extends VersionApiResponse {
  data?: VersionApiResponse['data'] & {
    updateType?: UpdateType // 更新类型
    incrementalUpdate?: IncrementalUpdateInfo // 增量更新信息
    fullUpdateUrl?: string // 全量更新下载链接
    incrementalUpdateUrl?: string // 增量更新下载链接
    supportedPlatforms?: string[] // 支持的平台
    minimumVersion?: string // 支持增量更新的最低版本
  }
}
