/**
 * 更新状态枚举
 */
export enum UpdateStatus {
  CHECKING = 'checking', // 正在检查更新
  AVAILABLE = 'available', // 有可用更新
  NOT_AVAILABLE = 'not-available', // 没有可用更新
  DOWNLOADING = 'downloading', // 正在下载更新
  DOWNLOADED = 'downloaded', // 更新已下载完成
  ERROR = 'error', // 更新过程中出现错误
  INSTALLING = 'installing', // 正在安装更新
}

/**
 * 更新信息接口
 */
export interface UpdateInfo {
  status: UpdateStatus // 当前更新状态，使用前面定义的 UpdateStatus 枚举
  version?: string // 可选，更新版本号（当有可用更新或下载完成时提供）
  releaseNotes?: string // 可选，更新说明/发布日志（当有可用更新或下载完成时提供）
  progress?: { // 可选，下载进度信息（仅在下载过程中提供）
    percent: number // 下载进度百分比
    bytesPerSecond: number // 下载速度（字节/秒）
    total: number // 需要下载的总字节数
    transferred: number // 已下载的字节数
  }
  error?: string // 可选，错误信息（仅在更新出错时提供）
}

/**
 * JSON API 版本信息接口
 * 用于从服务器获取版本信息的JSON响应格式
 */
export interface VersionApiResponse {
  success: boolean // 请求是否成功
  data?: {
    version: string // 最新版本号
    releaseNotes?: string // 发布说明
    downloadUrl?: string // 下载链接
    publishedAt?: string // 发布时间
    isForced?: boolean // 是否强制更新
    minVersion?: string // 最低支持版本
  }
  error?: string // 错误信息
}

/**
 * 版本检查配置
 */
export interface VersionCheckConfig {
  apiUrl: string // API 接口地址
  timeout?: number // 请求超时时间（毫秒），默认 10000
  headers?: Record<string, string> // 自定义请求头
}
