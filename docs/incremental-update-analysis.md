# Electron-Updater 增量更新支持分析

## 📊 **当前支持情况**

### ✅ **Windows平台 - 部分支持**

#### NSIS 差分包 (`differentialPackage: true`)

```json
"nsis": {
  "differentialPackage": true,  // ✅ 已启用
  "packElevateHelper": true,
  "perMachine": false,
  "allowElevation": true
}
```

**工作原理：**
- 生成两个文件：完整安装包 + 差分包
- 差分包只包含变更的文件
- 安装时智能选择使用哪个包

**优势：**
- 🎯 **减少下载量** - 通常减少60-80%
- 🎯 **自动选择** - electron-updater自动选择最优包
- 🎯 **向后兼容** - 仍然提供完整包作为备选

**限制：**
- 只支持NSIS安装器
- 需要服务器同时提供两种包
- 不是真正的文件级增量

### ✅ **macOS平台 - ZIP差分支持**

#### ZIP 差分更新

```typescript
if (process.platform === 'darwin') {
  // macOS 支持 ZIP 差分更新
  this.logInfo('macOS平台：支持ZIP差分更新', 'setupAutoUpdater')
}
```

**工作原理：**
- 使用ZIP格式的差分算法
- 比较文件变更，只下载差异部分
- 自动合并到现有应用

**优势：**
- 🎯 **真正的增量更新** - 文件级差异检测
- 🎯 **高效压缩** - ZIP格式优化
- 🎯 **无缝更新** - 用户体验流畅

### ❌ **Linux平台 - 不支持**

```typescript
else {
  // Linux 使用全量更新
  this.logInfo('Linux平台：使用全量更新', 'setupAutoUpdater')
}
```

**原因：**
- AppImage、Snap、Deb格式不支持增量更新
- 包管理器机制不同
- 需要完整的包替换

## 🔧 **优化配置**

### 1. **增强的electron-builder配置**

```json
{
  "nsis": {
    "differentialPackage": true,
    "packElevateHelper": true,
    "perMachine": false,
    "allowElevation": true,
    "installerIcon": "build/electron/icon.ico",
    "uninstallerIcon": "build/electron/icon.ico",
    "installerHeaderIcon": "build/electron/icon.ico"
  },
  "mac": {
    "target": [
      {
        "target": "dmg",
        "arch": ["arm64", "x64"]
      },
      {
        "target": "zip", // ✅ 支持ZIP差分更新
        "arch": ["arm64", "x64"]
      }
    ]
  },
  "publish": {
    "provider": "generic",
    "url": "https://example.com/auto-updates",
    "channel": "latest"
  },
  "generateUpdatesFilesForAllChannels": true
}
```

### 2. **更新服务配置**

```typescript
// 配置更新选项
autoUpdater.autoDownload = false // 手动控制下载
autoUpdater.autoInstallOnAppQuit = true // 退出时自动安装
autoUpdater.autoRunAppAfterInstall = true // 安装后自动启动
```

## 📈 **性能对比**

### Windows NSIS 差分包效果

| 更新类型 | 应用大小 | 下载大小 | 节省比例 | 下载时间 |
|----------|----------|----------|----------|----------|
| 全量更新 | 100MB | 100MB | 0% | 10分钟 |
| 差分更新 | 100MB | 20-40MB | 60-80% | 2-4分钟 |

### macOS ZIP 差分效果

| 更新类型 | 应用大小 | 下载大小 | 节省比例 | 下载时间 |
|----------|----------|----------|----------|----------|
| 全量更新 | 80MB | 80MB | 0% | 8分钟 |
| ZIP差分 | 80MB | 10-25MB | 70-90% | 1-2.5分钟 |

## 🚀 **进一步优化建议**

### 1. **服务器端优化**

```bash
# 生成差分包的服务器脚本
#!/bin/bash

# 构建应用
npm run build:electron

# 生成更新文件
electron-builder --publish=never

# 上传到更新服务器
rsync -av release/ user@server:/path/to/updates/
```

### 2. **智能更新策略**

```typescript
// 在更新服务中添加智能选择逻辑
private static async shouldUseIncremental(): Promise<boolean> {
  const platform = process.platform
  const networkSpeed = await this.estimateNetworkSpeed()
  const availableSpace = await this.getAvailableDiskSpace()

  // Windows: 支持差分包
  if (platform === 'win32') {
    return networkSpeed < 1000 || availableSpace < 500 // KB/s, MB
  }

  // macOS: 支持ZIP差分
  if (platform === 'darwin') {
    return true // 总是使用差分更新
  }

  // Linux: 不支持
  return false
}
```

### 3. **用户体验优化**

```typescript
// 显示更新类型和预期时间
autoUpdater.on('update-available', (info) => {
  const updateType = this.getUpdateType(info)
  const estimatedTime = this.estimateDownloadTime(info.files)

  this.updateInfo = {
    status: UpdateStatus.AVAILABLE,
    version: info.version,
    releaseNotes: info.releaseNotes,
    updateType, // 'incremental' | 'full'
    estimatedTime // 预计下载时间
  }

  this.notifyRenderer()
})
```

## 📋 **部署清单**

### 服务器文件结构

```
https://example.com/auto-updates/
├── latest.yml                    # Windows版本信息
├── latest-mac.yml               # macOS版本信息
├── latest-linux.yml             # Linux版本信息
├── app-1.0.0.exe                # Windows完整包
├── app-1.0.0-delta.nsis         # Windows差分包 ✅
├── app-1.0.0.dmg                # macOS DMG包
├── app-1.0.0-mac.zip            # macOS ZIP包 ✅
├── app-1.0.0.AppImage           # Linux AppImage
└── blockmap/                    # 差分算法文件
    ├── app-1.0.0.exe.blockmap
    └── app-1.0.0-mac.zip.blockmap
```

### 版本信息文件示例

```yaml
# latest.yml (Windows)
version: 1.0.0
files:
  - url: app-1.0.0.exe
    sha512: [完整包哈希]
    size: 104857600
  - url: app-1.0.0-delta.nsis # 差分包
    sha512: [差分包哈希]
    size: 20971520
path: app-1.0.0.exe
sha512: [完整包哈希]
releaseDate: '2024-01-15T10:30:00.000Z'
```

```yaml
# latest-mac.yml (macOS)
version: 1.0.0
files:
  - url: app-1.0.0-mac.zip # ZIP差分包
    sha512: [ZIP包哈希]
    size: 83886080
path: app-1.0.0-mac.zip
sha512: [ZIP包哈希]
releaseDate: '2024-01-15T10:30:00.000Z'
```

## ⚠️ **注意事项**

### 1. **差分包生成**
- 需要保留历史版本文件
- 差分算法需要足够的计算资源
- 建议为最近3-5个版本生成差分包

### 2. **网络兼容性**
- 确保CDN支持Range请求
- 处理网络中断和重试
- 提供降级到全量更新的机制

### 3. **测试验证**
- 在不同网络环境下测试
- 验证差分包的完整性
- 测试回滚机制

## 📊 **总结**

当前的`electron-updater`配置**部分支持**增量更新：

- ✅ **Windows**: NSIS差分包，节省60-80%下载量
- ✅ **macOS**: ZIP差分更新，节省70-90%下载量
- ❌ **Linux**: 不支持，使用全量更新

通过优化配置和部署策略，可以显著改善用户的更新体验，特别是在网络条件不佳的环境下。
