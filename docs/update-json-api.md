# JSON API 版本更新使用指南

本文档介绍如何在Electron应用中使用JSON API来检查版本更新。

## 概述

除了传统的`electron-updater`文件服务器更新方式，我们还支持通过JSON API来获取版本信息。这种方式更加灵活，可以自定义更新逻辑和用户体验。

## API接口格式

### 请求格式

```http
GET /api/version HTTP/1.1
Host: your-api-server.com
Content-Type: application/json
User-Agent: YourApp/1.0.0
```

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "data": {
    "version": "1.2.0",
    "releaseNotes": "修复了一些bug，增加了新功能",
    "downloadUrl": "https://example.com/download/app-1.2.0.exe",
    "publishedAt": "2024-01-15T10:30:00Z",
    "isForced": false,
    "minVersion": "1.0.0"
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "error": "版本信息获取失败"
}
```

### 字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `success` | boolean | 是 | 请求是否成功 |
| `data` | object | 否 | 版本数据（成功时提供） |
| `data.version` | string | 是 | 最新版本号 |
| `data.releaseNotes` | string | 否 | 发布说明 |
| `data.downloadUrl` | string | 否 | 下载链接 |
| `data.publishedAt` | string | 否 | 发布时间（ISO格式） |
| `data.isForced` | boolean | 否 | 是否强制更新 |
| `data.minVersion` | string | 否 | 最低支持版本 |
| `error` | string | 否 | 错误信息（失败时提供） |

## 使用方法

### 1. 基本使用

```typescript
import { checkVersionFromAPI, createVersionCheckConfig } from '@/utils/update'

// 创建配置
const config = createVersionCheckConfig('https://api.example.com/version')

// 检查版本
const result = await checkVersionFromAPI(config)

if (result.success && result.data) {
  console.log('API响应:', result.data)
} else {
  console.error('检查失败:', result.error)
}
```

### 2. 高级配置

```typescript
import { checkVersionFromAPI, createVersionCheckConfig } from '@/utils/update'

// 创建带自定义配置的版本检查配置
const config = createVersionCheckConfig('https://api.example.com/version', {
  timeout: 15000, // 15秒超时
  headers: {
    'Authorization': 'Bearer your-token',
    'X-App-Version': '1.0.0',
    'X-Platform': process.platform,
  }
})

const result = await checkVersionFromAPI(config)
```

### 3. 监听更新状态

```typescript
import { onUpdateStatus } from '@/utils/update'

// 设置监听器
const removeListener = onUpdateStatus((updateInfo) => {
  console.log('更新状态:', updateInfo.status)
  
  switch (updateInfo.status) {
    case 'checking':
      console.log('正在检查更新...')
      break
    case 'available':
      console.log(`发现新版本: ${updateInfo.version}`)
      break
    case 'not-available':
      console.log('当前已是最新版本')
      break
    case 'error':
      console.error('更新检查失败:', updateInfo.error)
      break
  }
})

// 记得在适当时候清理监听器
// removeListener()
```

### 4. Vue组件中使用

```vue
<template>
  <div>
    <button @click="checkFromAPI" :disabled="isChecking">
      {{ isChecking ? '检查中...' : '从API检查更新' }}
    </button>
    <div v-if="updateInfo.status === 'available'">
      发现新版本: {{ updateInfo.version }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { checkVersionFromAPI, createVersionCheckConfig, onUpdateStatus } from '@/utils/update'

const isChecking = ref(false)
const updateInfo = ref({ status: 'not-available' })

// 设置状态监听
onUpdateStatus((info) => {
  updateInfo.value = info
})

const checkFromAPI = async () => {
  isChecking.value = true
  try {
    const config = createVersionCheckConfig('https://api.example.com/version')
    await checkVersionFromAPI(config)
  } finally {
    isChecking.value = false
  }
}
</script>
```

## 服务器端实现示例

### Node.js + Express

```javascript
app.get('/api/version', (req, res) => {
  try {
    // 获取当前最新版本信息
    const latestVersion = getCurrentLatestVersion()
    
    res.json({
      success: true,
      data: {
        version: latestVersion.version,
        releaseNotes: latestVersion.releaseNotes,
        downloadUrl: latestVersion.downloadUrl,
        publishedAt: latestVersion.publishedAt,
        isForced: latestVersion.isForced || false,
        minVersion: latestVersion.minVersion || '1.0.0'
      }
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取版本信息失败'
    })
  }
})
```

### PHP

```php
<?php
header('Content-Type: application/json');

try {
    // 获取版本信息的逻辑
    $versionInfo = getLatestVersionInfo();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'version' => $versionInfo['version'],
            'releaseNotes' => $versionInfo['release_notes'],
            'downloadUrl' => $versionInfo['download_url'],
            'publishedAt' => $versionInfo['published_at'],
            'isForced' => $versionInfo['is_forced'] ?? false,
            'minVersion' => $versionInfo['min_version'] ?? '1.0.0'
        ]
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取版本信息失败'
    ]);
}
?>
```

## 版本比较逻辑

系统会自动比较当前版本和API返回的版本号：

- 使用语义化版本号比较（如 1.0.0 vs 1.1.0）
- 支持带`v`前缀的版本号（如 v1.0.0）
- 当API返回的版本号大于当前版本时，会触发`available`状态
- 当版本号相同或更低时，会触发`not-available`状态

## 错误处理

系统会处理以下错误情况：

1. **网络错误**: 请求超时、连接失败等
2. **HTTP错误**: 4xx、5xx状态码
3. **JSON解析错误**: 响应格式不正确
4. **数据验证错误**: 必需字段缺失

所有错误都会通过更新状态监听器通知到UI层。

## 最佳实践

1. **设置合理的超时时间**: 建议10-30秒
2. **添加用户代理**: 包含应用名称和版本号
3. **处理网络异常**: 提供友好的错误提示
4. **缓存版本信息**: 避免频繁请求
5. **支持离线模式**: 网络不可用时的降级处理

## 与传统更新方式的对比

| 特性 | JSON API | electron-updater |
|------|----------|------------------|
| 灵活性 | 高 | 中 |
| 自定义逻辑 | 支持 | 有限 |
| 服务器要求 | 简单HTTP服务 | 文件服务器 |
| 更新控制 | 完全控制 | 自动化 |
| 实现复杂度 | 中 | 低 |

## 注意事项

1. JSON API方式只负责版本检查，实际的下载和安装仍需要其他机制
2. 确保API服务器的可用性和安全性
3. 考虑API的访问频率限制
4. 建议同时支持两种更新方式，提供更好的用户体验
