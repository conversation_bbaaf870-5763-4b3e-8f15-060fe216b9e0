# Electron 更新系统使用指南

## 📋 **概述**

本项目使用 `electron-updater` 作为唯一的应用更新解决方案，提供稳定可靠的自动更新功能。

## 🏗️ **架构设计**

```mermaid
graph TD
    A[应用启动] --> B[UpdateService初始化]
    B --> C[配置autoUpdater]
    C --> D[设置事件监听器]
    
    E[用户触发检查] --> F[checkForUpdates]
    F --> G[autoUpdater.checkForUpdatesAndNotify]
    
    G --> H{发现更新?}
    H -->|是| I[update-available事件]
    H -->|否| J[update-not-available事件]
    
    I --> K[显示更新通知]
    K --> L[用户选择下载]
    L --> M[downloadUpdate]
    M --> N[download-progress事件]
    N --> O[update-downloaded事件]
    O --> P[提示安装]
    P --> Q[installUpdate]
    Q --> R[quitAndInstall]
```

## 🔧 **配置说明**

### electron-builder 配置

```json
{
  "publish": {
    "provider": "generic",
    "url": "https://example.com/auto-updates",
    "channel": "latest"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "${productName}",
    "differentialPackage": true
  },
  "generateUpdatesFilesForAllChannels": true
}
```

### 主进程配置

```typescript
// electron/main/services/update-service.ts
autoUpdater.setFeedURL({
  provider: 'generic',
  url: 'https://example.com/auto-updates',
  channel: 'latest',
})
```

## 📚 **API 使用**

### 基础用法

```typescript
import { 
  checkForUpdates, 
  downloadUpdate, 
  installUpdate,
  getCurrentVersion,
  getUpdateInfo,
  onUpdateStatus 
} from '@/utils/update'

// 检查更新
const result = await checkForUpdates()
if (result.success) {
  console.log('检查更新成功')
}

// 下载更新
await downloadUpdate()

// 安装更新
await installUpdate()

// 获取当前版本
const version = await getCurrentVersion()

// 监听更新状态
const removeListener = onUpdateStatus((updateInfo) => {
  console.log('更新状态:', updateInfo.status)
  
  switch (updateInfo.status) {
    case 'checking':
      console.log('正在检查更新...')
      break
    case 'available':
      console.log(`发现新版本: ${updateInfo.version}`)
      break
    case 'downloading':
      console.log(`下载进度: ${updateInfo.progress?.percent}%`)
      break
    case 'downloaded':
      console.log('下载完成，可以安装')
      break
    case 'not-available':
      console.log('当前已是最新版本')
      break
    case 'error':
      console.error('更新失败:', updateInfo.error)
      break
  }
})

// 记得清理监听器
// removeListener()
```

### Vue 组件示例

```vue
<template>
  <div class="update-panel">
    <div class="version-info">
      <span>当前版本: {{ currentVersion }}</span>
    </div>
    
    <div class="update-status">
      <div class="status-text">{{ statusText }}</div>
      
      <!-- 进度条 -->
      <div v-if="updateInfo.progress" class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: updateInfo.progress.percent + '%' }"
        ></div>
        <span class="progress-text">
          {{ Math.round(updateInfo.progress.percent) }}%
        </span>
      </div>
    </div>
    
    <div class="actions">
      <button 
        @click="handleCheckUpdate" 
        :disabled="isChecking"
        class="btn btn-primary"
      >
        {{ isChecking ? '检查中...' : '检查更新' }}
      </button>
      
      <button 
        v-if="updateInfo.status === 'available'" 
        @click="handleDownload"
        :disabled="isDownloading"
        class="btn btn-success"
      >
        {{ isDownloading ? '下载中...' : '下载更新' }}
      </button>
      
      <button 
        v-if="updateInfo.status === 'downloaded'" 
        @click="handleInstall"
        class="btn btn-warning"
      >
        安装并重启
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  checkForUpdates, 
  downloadUpdate, 
  installUpdate,
  getCurrentVersion,
  getUpdateInfo,
  onUpdateStatus,
  getUpdateStatusText
} from '@/utils/update'
import type { UpdateInfo } from '../../electron/types/update-service'

const currentVersion = ref('未知')
const updateInfo = ref<UpdateInfo>({ status: 'not-available' as any })
const isChecking = ref(false)
const isDownloading = ref(false)

const statusText = computed(() => getUpdateStatusText(updateInfo.value.status))

const handleCheckUpdate = async () => {
  isChecking.value = true
  try {
    await checkForUpdates()
  } finally {
    isChecking.value = false
  }
}

const handleDownload = async () => {
  isDownloading.value = true
  try {
    await downloadUpdate()
  } finally {
    isDownloading.value = false
  }
}

const handleInstall = async () => {
  await installUpdate()
}

let removeUpdateListener: (() => void) | null = null

onMounted(async () => {
  // 获取当前版本
  currentVersion.value = await getCurrentVersion()
  
  // 获取当前更新状态
  updateInfo.value = await getUpdateInfo()
  
  // 设置更新状态监听器
  removeUpdateListener = onUpdateStatus((info) => {
    updateInfo.value = info
  })
})

onUnmounted(() => {
  if (removeUpdateListener) {
    removeUpdateListener()
  }
})
</script>

<style scoped>
.update-panel {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.version-info {
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.update-status {
  margin-bottom: 20px;
}

.status-text {
  margin-bottom: 10px;
  font-weight: 500;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4caf50;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}
</style>
```

## 🚀 **部署配置**

### 1. 更新服务器设置

确保您的更新服务器能够提供以下文件：

```
https://example.com/auto-updates/
├── latest.yml          # 版本信息文件
├── latest-mac.yml      # macOS版本信息
├── latest-linux.yml    # Linux版本信息
├── app-1.0.0.exe       # Windows安装包
├── app-1.0.0.dmg       # macOS安装包
└── app-1.0.0.AppImage  # Linux安装包
```

### 2. 版本信息文件格式

```yaml
# latest.yml
version: 1.0.0
files:
  - url: app-1.0.0.exe
    sha512: [文件SHA512哈希值]
    size: 52428800
path: app-1.0.0.exe
sha512: [文件SHA512哈希值]
releaseDate: '2024-01-15T10:30:00.000Z'
```

## 🔍 **调试和测试**

### 开发环境测试

```typescript
// 在开发环境中测试更新功能
if (process.env.NODE_ENV === 'development') {
  // 设置开发服务器
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: 'http://localhost:3000/updates'
  })
  
  // 强制检查更新
  autoUpdater.forceDevUpdateConfig = true
}
```

### 日志查看

更新日志会自动记录到：
- Windows: `%APPDATA%/[AppName]/logs/`
- macOS: `~/Library/Logs/[AppName]/`
- Linux: `~/.config/[AppName]/logs/`

## ⚠️ **注意事项**

1. **代码签名**: 生产环境必须对应用进行代码签名
2. **HTTPS**: 更新服务器必须使用HTTPS
3. **版本号**: 严格遵循语义化版本号规范
4. **测试**: 在发布前充分测试更新流程
5. **回滚**: 保留旧版本以便必要时回滚

## 🛠️ **常见问题**

### Q: 更新检查失败
A: 检查网络连接和更新服务器配置

### Q: 下载进度不显示
A: 确保监听了 `download-progress` 事件

### Q: 安装后版本没有更新
A: 检查版本号格式和应用签名

### Q: macOS 提示应用已损坏
A: 需要对应用进行公证(notarization)

这个简化的更新系统专注于 electron-updater 的核心功能，提供稳定可靠的更新体验。
